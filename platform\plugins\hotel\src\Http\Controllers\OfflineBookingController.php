<?php

namespace Botble\Hotel\Http\Controllers;

use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Botble\Base\Models\BaseModel;
use Botble\Hotel\Facades\HotelHelper;
use Botble\Hotel\Http\Requests\CreateOfflineBookingRequest;
use Botble\Hotel\Models\Booking;
use Botble\Hotel\Models\BookingAddress;
use Botble\Hotel\Models\BookingRoom;
use Botble\Hotel\Models\Customer;
use Botble\Hotel\Models\Room;
use Botble\Hotel\Models\Service;
use Botble\Hotel\Models\Currency;
use Botble\Hotel\Models\RoomCategory;
use Botble\Hotel\Enums\BookingStatusEnum;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Carbon\Carbon;

class OfflineBookingController extends BaseController
{
    public function __construct()
    {
        $this
            ->breadcrumb()
            ->add(trans('plugins/hotel::booking.name'), route('booking.index'))
            ->add(trans('plugins/hotel::booking.create_offline_booking'), route('booking.offline.create'));
    }

    /**
     * Show the offline booking creation form
     */
    public function create()
    {
        $this->pageTitle(trans('plugins/hotel::booking.create_offline_booking'));

        // Get all available rooms with their categories
        $rooms = Room::with(['category', 'currency'])
            ->where('status', 'published')
            ->orderBy('name')
            ->get();

        // Get all available services
        $services = Service::where('status', 'published')
            ->orderBy('name')
            ->get();

        // Get room categories for filtering
        $roomCategories = RoomCategory::where('status', 'published')
            ->orderBy('name')
            ->get();

        // Get default currency
        $defaultCurrency = Currency::where('is_default', true)->first();

        // Get booking statuses
        $bookingStatuses = BookingStatusEnum::labels();

        return view('plugins/hotel::offline-booking.create', compact(
            'rooms',
            'services',
            'roomCategories',
            'defaultCurrency',
            'bookingStatuses'
        ));
    }

    /**
     * Store the offline booking
     */
    public function store(CreateOfflineBookingRequest $request)
    {
        try {
            DB::beginTransaction();

            // Log the start of the booking creation process
            \Log::info('Starting offline booking creation', ['data' => $request->validated()]);

            try {
                // Find or create customer
                $customer = $this->findOrCreateCustomer($request);
                \Log::info('Customer processed', ['customer_id' => $customer->id]);

                // Get room details
                $room = Room::with(['currency'])->findOrFail($request->input('room_id'));
                \Log::info('Room found', ['room_id' => $room->id]);

                // Calculate booking details
                $bookingDetails = $this->calculateBookingDetails($request, $room);
                \Log::info('Booking details calculated', ['details' => $bookingDetails]);

                // Create the booking
                $booking = $this->createBooking($request, $customer, $room, $bookingDetails);
                \Log::info('Booking created', ['booking_id' => $booking->id]);

                // Create booking address
                $this->createBookingAddress($request, $booking);
                \Log::info('Booking address created');

                // Create booking room
                $this->createBookingRoom($request, $booking, $room, $bookingDetails);
                \Log::info('Booking room created');

                // Handle additional services if any
                if ($request->has('services') && is_array($request->input('services'))) {
                    $this->attachServices($booking, $request->input('services'));
                    \Log::info('Services attached');
                }

                DB::commit();
                \Log::info('Booking transaction committed successfully');

                return $this
                    ->httpResponse()
                    ->setPreviousUrl(route('booking.index'))
                    ->setNextUrl(route('booking.edit', $booking->id))
                    ->withCreatedSuccessMessage();

            } catch (\Exception $innerException) {
                DB::rollBack();
                \Log::error('Error in booking creation process: ' . $innerException->getMessage());
                \Log::error($innerException->getTraceAsString());
                throw $innerException;
            }
        } catch (\Exception $e) {
            \Log::error('Fatal error in offline booking: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());

            return $this
                ->httpResponse()
                ->setError()
                ->setMessage('Error creating offline booking. Please check the server logs for more details.');
        }
    }

    /**
     * Get available rooms via AJAX
     */
    public function getAvailableRooms(Request $request)
    {
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $numberOfRooms = (int) $request->input('number_of_rooms', 1);

        if (!$startDate || !$endDate) {
            return response()->json([
                'success' => false,
                'message' => 'Start date and end date are required.'
            ]);
        }

        try {
            $rooms = Room::with(['category', 'currency'])
                ->where('status', 'published')
                ->get()
                ->filter(function ($room) use ($startDate, $endDate, $request) {
                    // Check both availability and occupancy validation
                    $isAvailable = $room->isAvailableAt([
                        'start_date' => $startDate,
                        'end_date' => $endDate,
                        'rooms' => 1,
                    ]);
                    
                    $adults = (int) $request->input('adults', 1);
                    $children = (int) $request->input('children', 0);
                    // Use RoomCapacityService for correct occupancy validation (includes Luxury room fix)
                    $roomCapacityService = app(\Botble\Hotel\Services\RoomCapacityService::class);
                    $isValidOccupancy = $roomCapacityService->roomMatchesOccupancyRules($room, $adults, $children);
                    
                    return $isAvailable && $isValidOccupancy;
                })
                ->map(function ($room) use ($startDate, $endDate, $numberOfRooms) {
                    $nights = Carbon::parse($startDate)->diffInDays(Carbon::parse($endDate));
                    $totalPrice = $room->price * $numberOfRooms * $nights;

                    return [
                        'id' => $room->id,
                        'name' => $room->name,
                        'category' => $room->category->name ?? '',
                        'price' => $room->price,
                        'total_price' => $totalPrice,
                        'formatted_price' => format_price($room->price),
                        'formatted_total_price' => format_price($totalPrice),
                        'max_adults' => $room->max_adults,
                        'max_children' => $room->max_children,
                        'number_of_rooms' => $room->number_of_rooms,
                        'image' => $room->image,
                    ];
                })
                ->values();

            return response()->json([
                'success' => true,
                'rooms' => $rooms,
                'message' => count($rooms) . ' rooms available for selected dates.'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching available rooms: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Find existing customer or create new one
     */
    protected function findOrCreateCustomer(CreateOfflineBookingRequest $request)
    {
        try {
            $customer = Customer::where('email', $request->input('email'))->first();

            if (!$customer) {
                $customer = Customer::create([
                    'first_name' => $request->input('first_name'),
                    'last_name' => $request->input('last_name'),
                    'email' => $request->input('email'),
                    'phone' => $request->input('phone'),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            return $customer;
        } catch (\Exception $e) {
            \Log::error('Error creating/finding customer: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());
            throw $e;
        }
    }

    /**
     * Calculate booking details including pricing
     */
    protected function calculateBookingDetails(CreateOfflineBookingRequest $request, Room $room)
    {
        try {
            $startDate = Carbon::parse($request->input('start_date'));
            $endDate = Carbon::parse($request->input('end_date'));
            $nights = $endDate->diffInDays($startDate);
            $numberOfRooms = (int) $request->input('number_of_rooms', 1);

            // Calculate subtotal
            $subTotal = $room->price * $nights * $numberOfRooms;

            // Calculate tax (assuming 10% - you should get this from settings)
            $taxPercent = 10;
            $taxAmount = ($subTotal * $taxPercent) / 100;

            // Calculate total
            $totalAmount = $subTotal + $taxAmount;

            return [
                'nights' => $nights,
                'sub_total' => $subTotal,
                'tax_amount' => $taxAmount,
                'total_amount' => $totalAmount,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'number_of_rooms' => $numberOfRooms,
            ];
        } catch (\Exception $e) {
            \Log::error('Error calculating booking details: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());
            throw $e;
        }
    }

    /**
     * Create the main booking record
     */
    protected function createBooking(CreateOfflineBookingRequest $request, Customer $customer, Room $room, array $bookingDetails)
    {
        try {
            // Get next insert ID for booking number
            $nextId = BaseModel::determineIfUsingUuidsForId()
                ? Booking::query()->count() + 1
                : Booking::query()->max('id') + 1;

            // Generate booking number using HotelHelper
            $bookingNumber = HotelHelper::getBookingNumber($nextId);
            \Log::info('Generated booking number: ' . $bookingNumber);

            // Try to create booking with meta_data, fall back without it if column doesn't exist
            try {
                $booking = Booking::create([
                    'customer_id' => $customer->id,
                    'currency_id' => $room->currency_id,
                    'amount' => $bookingDetails['total_amount'],
                    'sub_total' => $bookingDetails['sub_total'],
                    'tax_amount' => $bookingDetails['tax_amount'],
                    'status' => $request->input('status', BookingStatusEnum::PENDING),
                    'number_of_guests' => $request->input('adults'),
                    'number_of_children' => $request->input('children', 0),
                    'requests' => $request->input('requests'),
                    'arrival_time' => $request->input('arrival_time'),
                    'booking_number' => $bookingNumber,
                    'transaction_id' => 'OFFLINE_' . Str::upper(Str::random(10)),
                    'meta_data' => [
                        'booking_type' => 'offline',
                        'created_by_admin' => true,
                        'payment_method' => $request->input('payment_method'),
                        'payment_notes' => $request->input('payment_notes'),
                    ],
                ]);

                // Create payment record for offline booking
                $this->createOfflinePayment($request, $booking, $bookingDetails);

                return $booking;
            } catch (\Exception $e) {
                $errorMessage = $e->getMessage();
                \Log::info('Caught exception during booking creation: ' . $errorMessage);

                if (strpos($errorMessage, 'meta_data') !== false ||
                    strpos($errorMessage, 'Unknown column') !== false ||
                    strpos($errorMessage, 'Column not found') !== false) {
                    // Column doesn't exist, create without meta_data but mark transaction_id as offline
                    \Log::info('meta_data column not found, creating booking without it');
                    $booking = Booking::create([
                        'customer_id' => $customer->id,
                        'currency_id' => $room->currency_id,
                        'amount' => $bookingDetails['total_amount'],
                        'sub_total' => $bookingDetails['sub_total'],
                        'tax_amount' => $bookingDetails['tax_amount'],
                        'status' => $request->input('status', BookingStatusEnum::PENDING),
                        'number_of_guests' => $request->input('adults'),
                        'number_of_children' => $request->input('children', 0),
                        'requests' => $request->input('requests'),
                        'arrival_time' => $request->input('arrival_time'),
                        'booking_number' => $bookingNumber,
                        'transaction_id' => 'OFFLINE_' . Str::upper(Str::random(10)),
                    ]);

                    // Create payment record for offline booking
                    $this->createOfflinePayment($request, $booking, $bookingDetails);

                    return $booking;
                }
                throw $e;
            }
        } catch (\Exception $e) {
            \Log::error('Error creating booking record: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());
            throw $e;
        }
    }

    /**
     * Create booking address record
     */
    protected function createBookingAddress(CreateOfflineBookingRequest $request, Booking $booking): void
    {
        BookingAddress::create([
            'booking_id' => $booking->id,
            'first_name' => $request->input('first_name'),
            'last_name' => $request->input('last_name'),
            'email' => $request->input('email'),
            'phone' => $request->input('phone'),
            'address' => $request->input('address'),
            'city' => $request->input('city'),
            'state' => $request->input('state'),
            'country' => $request->input('country'),
            'zip' => $request->input('zip'),
        ]);
    }

    /**
     * Create booking room record
     */
    protected function createBookingRoom(CreateOfflineBookingRequest $request, Booking $booking, Room $room, array $bookingDetails)
    {
        try {
            return BookingRoom::create([
                'booking_id' => $booking->id,
                'room_id' => $room->id,
                'room_name' => $room->name,
                'room_image' => $room->image,
                'price' => $room->price,
                'currency_id' => $room->currency_id,
                'number_of_rooms' => $bookingDetails['number_of_rooms'],
                'start_date' => $bookingDetails['start_date'],
                'end_date' => $bookingDetails['end_date'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        } catch (\Exception $e) {
            \Log::error('Error creating booking room record: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());
            throw $e;
        }
    }

    /**
     * Attach additional services to booking
     */
    protected function attachServices(Booking $booking, array $serviceIds): void
    {
        $services = Service::whereIn('id', $serviceIds)->get();

        foreach ($services as $service) {
            // For now, we'll just log the services since meta_data column doesn't exist
            \Log::info('Service attached to booking', [
                'booking_id' => $booking->id,
                'service_id' => $service->id,
                'service_name' => $service->name,
                'service_price' => $service->price,
            ]);

            // TODO: Create a booking_services table to properly store this relationship
        }
    }

    /**
     * Create payment record for offline booking
     */
    protected function createOfflinePayment(CreateOfflineBookingRequest $request, Booking $booking, array $bookingDetails): void
    {
        try {
            if (!class_exists('\Botble\Payment\Models\Payment')) {
                \Log::info('Payment plugin not available, skipping payment creation');
                return;
            }

            $paymentMethod = $request->input('payment_method', 'cash');
            $paymentStatus = $request->input('payment_status', 'pending');

            // Map payment method to enum values
            $paymentChannel = match($paymentMethod) {
                'cash', 'cash_on_delivery' => 'cod',
                'bank_transfer' => 'bank_transfer',
                default => 'cod'
            };

            // Map payment status to enum values
            $status = match($paymentStatus) {
                'completed', 'paid' => 'completed',
                'pending' => 'pending',
                'failed' => 'failed',
                default => 'pending'
            };

            $payment = \Botble\Payment\Models\Payment::create([
                'amount' => $booking->amount,
                'currency' => $booking->currency->title ?? 'USD',
                'charge_id' => 'OFFLINE_' . Str::upper(Str::random(10)),
                'payment_channel' => $paymentChannel,
                'status' => $status,
                'payment_type' => 'direct',
                'order_id' => $booking->id,
                'customer_id' => $booking->customer_id,
                'customer_type' => \Botble\Hotel\Models\Customer::class,
            ]);

            // Update booking with payment ID
            $booking->payment_id = $payment->id;
            $booking->save();

            \Log::info('Payment record created for offline booking', [
                'booking_id' => $booking->id,
                'payment_id' => $payment->id,
                'payment_method' => $paymentChannel,
                'status' => $status
            ]);

        } catch (\Exception $e) {
            \Log::error('Error creating payment record for offline booking: ' . $e->getMessage());
            // Don't throw the error as payment creation is not critical for booking creation
        }
    }
}
