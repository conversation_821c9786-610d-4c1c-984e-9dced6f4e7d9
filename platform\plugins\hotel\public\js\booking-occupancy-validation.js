/**
 * Booking Occupancy Validation
 * Handles client-side validation for room occupancy when making bookings
 */
$(document).ready(function() {
    // Define valid occupancy combinations for Standard Room
    const standardRoomCombinations = [
        { adults: 1, children: 2 }, // 1 adult, 2 children
        { adults: 2, children: 1 }, // 2 adults, 1 child
        { adults: 3, children: 0 }, // 3 adults, no children
        { adults: 1, children: 1 }, // 1 adult, 1 child
        { adults: 1, children: 0 }, // 1 adult, no children
        { adults: 2, children: 0 }  // 2 adults, no children
        // Note: 2 adults and 2 children is not allowed as it exceeds max_total of 3
    ];

    // Define valid occupancy combinations for Double Bed Room
    const doubleBedRoomCombinations = [
        { adults: 3, children: 1 }, // 3 adults, 1 child
        { adults: 3, children: 0 }, // 3 adults, no children
        { adults: 2, children: 2 }, // 2 adults, 2 children
        { adults: 2, children: 1 }, // 2 adults, 1 child
        { adults: 2, children: 0 }, // 2 adults, no children
        { adults: 1, children: 3 }, // 1 adult, 3 children
        { adults: 1, children: 2 }, // 1 adult, 2 children
        { adults: 1, children: 1 }, // 1 adult, 1 child
        { adults: 1, children: 0 }  // 1 adult, no children
        // Note: 4 adults is explicitly excluded
    ];

    // Define valid occupancy combinations for Luxury Room (couples only)
    const luxuryRoomCombinations = [
        { adults: 2, children: 0 },  // Only couples allowed (2 adults, no children)
         { adults: 1, children: 0 }  // 1 adult, no children
    ];

    // Get form elements - include all possible field names used in different forms
    const $roomSelect = $('select[name="room_id"], select[name="room_id_code"]');
    const $adultsInput = $('input[name="adults"], select[name="adults"], input[name="number_of_guests"], select[name="number_of_guests"]');
    const $childrenInput = $('input[name="children"], select[name="children"], input[name="number_of_children"], select[name="number_of_children"]');

    // Function to validate room occupancy based on room type
    function validateRoomOccupancy() {
        // If any of the required elements are missing, exit
        if (!$roomSelect.length || !$adultsInput.length) {
            return;
        }

        const adults = parseInt($adultsInput.val()) || 0;
        const children = parseInt($childrenInput.val()) || 0;
        const roomId = $roomSelect.val();

        // Remove any existing warnings
        $('.occupancy-warning').remove();
        $adultsInput.removeClass('is-invalid');
        $childrenInput.removeClass('is-invalid');

        // If no room is selected, exit
        if (!roomId) {
            return;
        }

        // Get the room type (this is a simplified approach - in a real system, you would
        // make an AJAX call to get the room type based on the room ID)
        const selectedRoomText = $roomSelect.find('option:selected').text();

        // Check if this is a Standard Room
        const isStandardRoom = selectedRoomText.indexOf('Standard Room') !== -1 ||
                              selectedRoomText.indexOf('STD') !== -1 ||
                              (roomId && roomId.toString().indexOf('STD-') === 0);

        // Check if this is a Double Bed Room
        const isDoubleBedRoom = selectedRoomText.indexOf('Double Bed') !== -1 ||
                               selectedRoomText.indexOf('DBR') !== -1 ||
                               (roomId && roomId.toString().indexOf('DBR-') === 0);

        // Check if this is a Luxury Room
        const isLuxuryRoom = selectedRoomText.indexOf('Luxury') !== -1 ||
                            selectedRoomText.indexOf('LUX') !== -1 ||
                            (roomId && roomId.toString().indexOf('LUX-') === 0);

        if (isStandardRoom) {
            validateStandardRoom(adults, children);
        } else if (isDoubleBedRoom) {
            validateDoubleBedRoom(adults, children);
        } else if (isLuxuryRoom) {
            validateLuxuryRoom(adults, children);
        }
    }

    // Function to validate Standard Room occupancy
    function validateStandardRoom(adults, children) {
        // Check if total exceeds max_total (3)
        if (adults + children > 3) {
            showWarning(
                'Standard Room',
                'For Standard Room, the maximum total occupancy is 3 people (adults + children).',
                [
                    '1 adult and 2 children',
                    '2 adults and 1 child',
                    '3 adults only',
                    '1 adult and 1 child',
                    '1 adult only',
                    '2 adults only'
                ]
            );
            return false;
        }

        // Check if the combination is one of the allowed combinations
        let isValid = false;
        for (const combo of standardRoomCombinations) {
            if (combo.adults === adults && combo.children === children) {
                isValid = true;
                break;
            }
        }

        if (!isValid) {
            showWarning(
                'Standard Room',
                'This combination of adults and children is not allowed for Standard Room.',
                [
                    '1 adult and 2 children',
                    '2 adults and 1 child',
                    '3 adults only',
                    '1 adult and 1 child',
                    '1 adult only',
                    '2 adults only'
                ]
            );
            return false;
        }

        return true;
    }

    // Function to validate Double Bed Room occupancy
    function validateDoubleBedRoom(adults, children) {
        // Check if it's 4 adults (not allowed)
        if (adults === 4 && children === 0) {
            showWarning(
                'Double Bed Room',
                'For Double Bed Room, the maximum occupancy is 4 people total, but the combination of 4 adults is not allowed.',
                [
                    '3 adults and 1 child',
                    '3 adults only',
                    '2 adults and 2 children',
                    '2 adults and 1 child',
                    '2 adults only',
                    '1 adult and 3 children',
                    '1 adult and 2 children',
                    '1 adult and 1 child',
                    '1 adult only'
                ]
            );
            return false;
        }

        // Check if total exceeds 4
        if (adults + children > 4) {
            showWarning(
                'Double Bed Room',
                'For Double Bed Room, the maximum total occupancy is 4 people (adults + children).',
                [
                    '3 adults and 1 child',
                    '3 adults only',
                    '2 adults and 2 children',
                    '2 adults and 1 child',
                    '2 adults only',
                    '1 adult and 3 children',
                    '1 adult and 2 children',
                    '1 adult and 1 child',
                    '1 adult only'
                ]
            );
            return false;
        }

        // Check if the combination is one of the allowed combinations
        let isValid = false;
        for (const combo of doubleBedRoomCombinations) {
            if (combo.adults === adults && combo.children === children) {
                isValid = true;
                break;
            }
        }

        if (!isValid) {
            showWarning(
                'Double Bed Room',
                'This combination of adults and children is not allowed for Double Bed Room.',
                [
                    '3 adults and 1 child',
                    '3 adults only',
                    '2 adults and 2 children',
                    '2 adults and 1 child',
                    '2 adults only',
                    '1 adult and 3 children',
                    '1 adult and 2 children',
                    '1 adult and 1 child',
                    '1 adult only'
                ]
            );
            return false;
        }

        return true;
    }

    // Function to validate Luxury Room occupancy
    function validateLuxuryRoom(adults, children) {
        // Check if the combination is one of the allowed combinations
        let isValid = false;
        for (const combo of luxuryRoomCombinations) {
            if (combo.adults === adults && combo.children === children) {
                isValid = true;
                break;
            }
        }

        if (!isValid) {
            showWarning(
                'Luxury Room',
                'This combination of adults and children is not allowed for Luxury Room.',
                [
                    '2 adults only (couples)',
                    '1 adult only'
                ]
            );
            return false;
        }

        return true;
    }

    // Helper function to show warning message
    function showWarning(roomTypeName, message, allowedDescriptions) {
        // Create list items for allowed combinations
        let listItems = '';
        allowedDescriptions.forEach(desc => {
            listItems += `<li>${desc}</li>`;
        });

        // Add warning message
        const warningHtml = `
            <div class="occupancy-warning alert alert-warning mt-2">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>${message}</strong><br>
                Only these combinations are allowed:
                <ul>
                    ${listItems}
                </ul>
            </div>
        `;

        // Find the appropriate place to add the warning
        if ($childrenInput.length) {
            $childrenInput.closest('.form-group, .row').after(warningHtml);
        } else {
            $adultsInput.closest('.form-group, .row').after(warningHtml);
        }

        // Highlight the fields
        $adultsInput.addClass('is-invalid');
        if ($childrenInput.length) {
            $childrenInput.addClass('is-invalid');
        }
    }

    // Validate on room change
    $roomSelect.on('change', function() {
        validateRoomOccupancy();
    });

    // Validate on adults change
    $adultsInput.on('change keyup', function() {
        validateRoomOccupancy();
    });

    // Validate on children change
    $childrenInput.on('change keyup', function() {
        validateRoomOccupancy();
    });

    // Initial validation on page load
    validateRoomOccupancy();

    // Add form submission validation
    $('form').on('submit', function(e) {
        const adults = parseInt($adultsInput.val()) || 0;
        const children = parseInt($childrenInput.val()) || 0;
        const roomId = $roomSelect.val();

        // If no room is selected, exit
        if (!roomId) {
            return true;
        }

        // Get the room type
        const selectedRoomText = $roomSelect.find('option:selected').text();

        // Check if this is a Standard Room
        const isStandardRoom = selectedRoomText.indexOf('Standard Room') !== -1 ||
                              selectedRoomText.indexOf('STD') !== -1 ||
                              (roomId && roomId.toString().indexOf('STD-') === 0);

        // Check if this is a Double Bed Room
        const isDoubleBedRoom = selectedRoomText.indexOf('Double Bed') !== -1 ||
                               selectedRoomText.indexOf('DBR') !== -1 ||
                               (roomId && roomId.toString().indexOf('DBR-') === 0);

        // Check if this is a Luxury Room
        const isLuxuryRoom = selectedRoomText.indexOf('Luxury') !== -1 ||
                            selectedRoomText.indexOf('LUX') !== -1 ||
                            (roomId && roomId.toString().indexOf('LUX-') === 0);

        if (isStandardRoom) {
            // Validate Standard Room occupancy
            if (!validateStandardRoom(adults, children)) {
                e.preventDefault();

                // Scroll to the warning if it exists
                if ($('.occupancy-warning').length) {
                    $('html, body').animate({
                        scrollTop: $('.occupancy-warning').offset().top - 100
                    }, 500);
                }

                return false;
            }
        } else if (isDoubleBedRoom) {
            // Validate Double Bed Room occupancy
            if (!validateDoubleBedRoom(adults, children)) {
                e.preventDefault();

                // Scroll to the warning if it exists
                if ($('.occupancy-warning').length) {
                    $('html, body').animate({
                        scrollTop: $('.occupancy-warning').offset().top - 100
                    }, 500);
                }

                return false;
            }
        } else if (isLuxuryRoom) {
            // Validate Luxury Room occupancy
            if (!validateLuxuryRoom(adults, children)) {
                e.preventDefault();

                // Scroll to the warning if it exists
                if ($('.occupancy-warning').length) {
                    $('html, body').animate({
                        scrollTop: $('.occupancy-warning').offset().top - 100
                    }, 500);
                }

                return false;
            }
        }

        return true;
    });
});
