# MRhote Hotel Booking System - Comprehensive Demo Guide

## Table of Contents
1. [Project Overview](#project-overview)
2. [System Architecture](#system-architecture)
3. [Online Booking Process](#online-booking-process)
4. [Offline Booking Process](#offline-booking-process)
5. [Feature Comparison](#feature-comparison)
6. [Known Issues & Limitations](#known-issues--limitations)
7. [Technical Requirements](#technical-requirements)
8. [Demo Walkthrough](#demo-walkthrough)
9. [Appendix](#appendix)
   - [A. Troubleshooting Common Issues](#a-troubleshooting-common-issues)
   - [B. API Endpoints Reference](#b-api-endpoints-reference)
   - [C. Database Schema Overview](#c-database-schema-overview)
   - [D. Security Considerations](#d-security-considerations)
   - [E. Performance Monitoring](#e-performance-monitoring)

---

## Project Overview

**MRhote** is a comprehensive hotel booking management system built on Laravel/Botble CMS that provides both online and offline booking capabilities. The system is designed to handle room reservations, customer management, payment processing, and administrative oversight.

### Key Features
- **Dual Booking System**: Online (customer-facing) and Offline (admin-managed) booking processes
- **Room Management**: Dynamic room availability, pricing, and inventory management
- **Customer Management**: Registration, profiles, and booking history
- **Payment Integration**: Multiple payment methods and processing
- **Administrative Dashboard**: Complete booking oversight and management tools

---

## System Architecture

### Frontend Components
- **Theme**: Miranda theme with responsive design
- **Booking Forms**: Separate interfaces for online and offline bookings
- **Room Display**: Dynamic room listings with availability status
- **Customer Portal**: Account management and booking history

### Backend Components
- **Controllers**: 
  - `PublicController`: Handles online booking flow
  - `OfflineBookingController`: Manages admin-created bookings
  - `BookingController`: General booking management
- **Models**: Room, Booking, Customer, RoomInventory
- **Services**: RoomSearchService, RoomCapacityService

---

## Online Booking Process

### Step 1: Room Search & Selection
**Location**: Public website rooms page (`/rooms`)

**Process**:
1. Customer visits the rooms page
2. Uses search form to filter by:
   - Check-in date
   - Check-out date
   - Number of adults
   - Number of children
   - Number of rooms

**Screenshot Placeholder**: `[SCREENSHOT: Online room search form]`

**Key Features**:
- Real-time availability checking
- Dynamic pricing calculation
- Room filtering based on occupancy rules
- Visual room cards with amenities

### Step 2: Room Details & Availability Check
**Process**:
1. System validates room availability using `Room::isAvailableAt()`
2. Calculates total price including taxes
3. Displays room information with "Book Now" button
4. Shows all rooms with availability status

**Screenshot Placeholder**: `[SCREENSHOT: Room listing with availability]`

**Technical Implementation**:
```php
// PublicController::getRooms() - Line 119-132
foreach ($allRooms as $allRoom) {
    // Check occupancy validation first using RoomCapacityService
    $roomCapacityService = app(\Botble\Hotel\Services\RoomCapacityService::class);
    $isValidOccupancy = $roomCapacityService->roomMatchesOccupancyRules($allRoom, $adults, $children);

    // Check if room is available for the requested dates
    $isAvailable = $allRoom->isAvailableAt($condition);

    // Calculate price regardless of availability
    $allRoom->total_price = $allRoom->getRoomTotalPrice($startDate, $endDate);

    // Add availability status to the room object
    $allRoom->is_available = $isAvailable && $isValidOccupancy;
    $allRoom->is_valid_occupancy = $isValidOccupancy;
}
```

**Room Display Template** (`room-item.blade.php`):
```php
@if ($room->total_price)
    <form action="{{ route('public.booking') }}" method="POST">
        @csrf
        <input type="hidden" name="room_id" value="{{ $room->id }}">
        <input type="hidden" name="start_date" value="{{ request()->query('start_date') }}">
        <input type="hidden" name="end_date" value="{{ request()->query('end_date') }}">
        <input type="hidden" name="adults" value="{{ request()->query('adults', 1) }}">
        <input type="hidden" name="children" value="{{ request()->query('children', 0) }}">
        <button type="submit" class="main-btn btn-filled booking-button">
            {{ __('Book now for :price', ['price' => format_price($room->total_price)]) }}
        </button>
    </form>
@endif
```

**Key Features**:
- **Occupancy Validation**: Uses `RoomCapacityService` for accurate guest capacity checking
- **Dynamic Pricing**: Real-time price calculation based on dates and room rates
- **Availability Status**: Visual indicators for room availability
- **Responsive Design**: Mobile-optimized room cards with amenities display

### Step 3: Booking Initiation
**Route**: `POST /booking`
**Controller**: `PublicController::postBooking()`

**Process**:
1. Customer clicks "Book Now" button
2. System validates room availability again
3. Creates booking session with unique token
4. Redirects to booking form

**Screenshot Placeholder**: `[SCREENSHOT: Book now button and room selection]`

**Technical Implementation**:
```php
// PublicController::postBooking() - Line 324-357
public function postBooking(InitBookingRequest $request, BaseHttpResponse $response)
{
    abort_if(! HotelHelper::isBookingEnabled(), 404);

    $room = Room::query()
        ->with(['currency', 'category'])
        ->findOrFail($request->input('room_id'));

    $condition = [
        'start_date' => HotelHelper::dateFromRequest($request->input('start_date')),
        'end_date' => HotelHelper::dateFromRequest($request->input('end_date')),
        'adults' => $request->integer('adults', 1),
        'children' => $request->integer('children'),
        'rooms' => $request->integer('rooms', 1),
    ];

    // Double-check availability before proceeding
    if (! $room->isAvailableAt($condition)) {
        return $response
            ->setError()
            ->setMessage(__('This room is not available for booking from :start_date to :end_date!'))
            ->withInput();
    }

    // Create secure session token
    $token = md5(Str::random(40));

    session([
        $token => $request->except(['_token']),
        'checkout_token' => $token,
    ]);

    return $response->setNextUrl(route('public.booking.form', $token));
}
```

**Security Features**:
- **Double Validation**: Re-checks room availability to prevent race conditions
- **Secure Tokens**: Uses MD5 hash of random string for session security
- **Input Sanitization**: Validates and sanitizes all user inputs
- **Session Management**: Temporary storage of booking data

### Step 4: Customer Information Form
**Location**: `/booking/{token}`
**View**: `hotel.booking`
**Controller**: `PublicController::getBooking()`

**Process**:
1. Customer fills out personal information
2. Reviews booking details and pricing
3. Selects additional services (if available)
4. Applies coupon codes (if applicable)

**Screenshot Placeholder**: `[SCREENSHOT: Customer information form]`

**Form Structure** (`hotel/booking.blade.php`):
```html
<form class="booking-form-main payment-checkout-form"
      action="{{ route('public.booking.checkout') }}" method="POST">
    @csrf
    <input type="hidden" name="token" value="{{ $token }}">
    <input type="hidden" name="amount" value="{{ $total }}">
    <input type="hidden" name="room_id" value="{{ $room->id }}">
    <input type="hidden" name="start_date" value="{{ $startDate->format('d-m-Y') }}">
    <input type="hidden" name="end_date" value="{{ $endDate->format('d-m-Y') }}">

    <!-- Customer Information Section -->
    <!-- Personal Details, Contact Info, Address -->

    <!-- Booking Summary Section -->
    <!-- Room details, pricing breakdown, taxes -->

    <!-- Additional Services Section -->
    <!-- Optional services with pricing -->

    <!-- Payment Section -->
    <!-- Payment method selection and processing -->
</form>
```

**Key Form Sections**:
1. **Personal Information**:
   - Full name, email, phone number
   - Address details (street, city, state, country)
   - Emergency contact information

2. **Booking Summary**:
   - Room details and selected dates
   - Number of guests (adults/children)
   - Price breakdown (room rate, taxes, services)
   - Total amount calculation

3. **Additional Services**:
   - Optional hotel services (spa, dining, tours)
   - Service pricing and descriptions
   - Dynamic total calculation

4. **Special Requests**:
   - Text area for customer preferences
   - Accessibility requirements
   - Dietary restrictions or special occasions

**Data Processing**:
```php
// PublicController::getBooking() - Line 441-479
$room->total_price = $room->getRoomTotalPrice($startDate, $endDate, $rooms);
$amount = $room->total_price + Arr::get($sessionData, 'service_amount', 0);
$taxAmount = $room->tax->percentage * $amount / 100;
$couponAmount = Arr::get($sessionData, 'coupon_amount', 0);
$total = $amount + $taxAmount - $couponAmount;

return Theme::scope('hotel.booking', compact(
    'room', 'services', 'startDate', 'endDate', 'adults', 'children',
    'rooms', 'amount', 'total', 'taxAmount', 'token', 'customer',
    'selectedServices', 'couponCode', 'couponAmount'
))->render();
```

### Step 5: Payment & Checkout
**Route**: `POST /checkout`
**Controller**: `PublicController::postCheckout()`

**Process**:
1. System calculates final amount including taxes
2. Processes payment through integrated gateway
3. Creates booking record in database
4. Assigns room ID code automatically
5. Sends confirmation email

**Screenshot Placeholder**: `[SCREENSHOT: Payment form and checkout]`

### Step 6: Booking Confirmation
**Location**: `/booking-information/{transaction_id}`

**Process**:
1. Displays booking confirmation details
2. Shows transaction ID and booking number
3. Provides booking management options
4. Sends confirmation email to customer

**Screenshot Placeholder**: `[SCREENSHOT: Booking confirmation page]`

---

## Offline Booking Process

### Step 1: Admin Access
**Location**: Admin dashboard (`/admin/hotel/booking/offline/create`)
**Access**: Requires admin authentication and booking.create permission

**Screenshot Placeholder**: `[SCREENSHOT: Admin dashboard booking menu]`

### Step 2: Customer Information Entry
**Process**:
1. Admin enters customer details manually
2. Fills comprehensive customer form including:
   - Personal information
   - Contact details
   - Address information

**Screenshot Placeholder**: `[SCREENSHOT: Offline booking customer form]`

**Form Sections**:
- Customer Information (required fields marked)
- Contact Details
- Address Information (city, state, country, zip)

### Step 3: Booking Parameters
**Process**:
1. Admin sets booking dates
2. Specifies number of adults and children
3. Sets number of rooms required
4. Clicks "Search Available Rooms"

**Screenshot Placeholder**: `[SCREENSHOT: Booking parameters form]`

**Key Fields**:
- Start Date (date picker with validation)
- End Date (automatically validates after start date)
- Adults (dropdown 1-10)
- Children (dropdown 0-10)
- Number of Rooms (dropdown 1-10)

### Step 4: Room Search & Selection
**AJAX Endpoint**: `/admin/hotel/booking/offline/get-available-rooms`
**Controller**: `OfflineBookingController::getAvailableRooms()`

**Process**:
1. System searches all published rooms
2. **Shows ALL rooms** (both available and unavailable)
3. Displays availability status with visual indicators
4. Admin selects appropriate room

**Screenshot Placeholder**: `[SCREENSHOT: Room search results with availability badges]`

**Technical Implementation** (Fixed for Parity):
```php
// OfflineBookingController::getAvailableRooms() - Line 154-174
$rooms = Room::with(['category', 'currency'])
    ->where('status', 'published')
    ->get()
    ->map(function ($room) use ($startDate, $endDate, $request) {
        // Check both availability and occupancy validation
        $isAvailable = $room->isAvailableAt([
            'start_date' => $startDate,
            'end_date' => $endDate,
            'rooms' => 1,
        ]);

        $adults = (int) $request->input('adults', 1);
        $children = (int) $request->input('children', 0);

        // Use RoomCapacityService for correct occupancy validation
        $roomCapacityService = app(\Botble\Hotel\Services\RoomCapacityService::class);
        $isValidOccupancy = $roomCapacityService->roomMatchesOccupancyRules($room, $adults, $children);

        // Return ALL rooms with availability status
        return [
            'id' => $room->id,
            'name' => $room->name,
            'category' => $room->category->name ?? 'N/A',
            'price' => $room->price,
            'formatted_price' => format_price($room->price),
            'is_available' => $isAvailable && $isValidOccupancy,
            'availability_reason' => !$isAvailable ? 'Not available for dates' :
                                   (!$isValidOccupancy ? 'Occupancy limit exceeded' : 'Available')
        ];
    });
```

**Frontend Display Logic** (`create.blade.php`):
```javascript
function displayAvailableRooms(rooms) {
    rooms.forEach(function(room) {
        const availabilityBadge = room.is_available ?
            '<span class="badge bg-success">Available</span>' :
            '<span class="badge bg-danger">Unavailable</span>';

        const buttonState = room.is_available ?
            'btn-primary' : 'btn-secondary disabled';

        const roomCard = `
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card room-card ${room.is_available ? '' : 'opacity-75'}"
                     data-room-id="${room.id}">
                    <div class="card-body">
                        <h6 class="card-title">${room.name} ${availabilityBadge}</h6>
                        <p class="card-text">
                            <small class="text-muted">${room.category}</small><br>
                            <strong>Price: ${room.formatted_price}</strong>
                        </p>
                        <button type="button"
                                class="btn ${buttonState} btn-sm select-room"
                                data-room='${JSON.stringify(room)}'
                                ${!room.is_available ? 'disabled' : ''}>
                            ${room.is_available ? 'Select Room' : 'Unavailable'}
                        </button>
                    </div>
                </div>
            </div>
        `;
    });
}
```

**Display Features**:
- ✅ **Available rooms**: Green badge, enabled selection button
- ❌ **Unavailable rooms**: Red badge, disabled selection button, reduced opacity
- **Room details**: Name, category, price, occupancy limits
- **Summary information**: Total rooms found, available count
- **Availability reasons**: Specific reason why room is unavailable

### Step 5: Additional Information
**Process**:
1. Admin enters additional booking details:
   - Arrival time
   - Booking status
   - Special requests
   - Additional services
   - Payment information

**Screenshot Placeholder**: `[SCREENSHOT: Additional information form]`

### Step 6: Booking Creation
**Route**: `POST /admin/hotel/booking/offline/store`
**Controller**: `OfflineBookingController::store()`

**Process**:
1. Validates all form data
2. Creates or finds customer record
3. Creates booking with offline metadata
4. Assigns room ID code
5. Creates booking address record
6. Redirects to booking management

**Screenshot Placeholder**: `[SCREENSHOT: Booking creation success]`

**Technical Implementation**:
```php
// OfflineBookingController::store() - Line 74-101
public function store(CreateOfflineBookingRequest $request)
{
    try {
        DB::beginTransaction();

        // Log the start of the booking creation process
        \Log::info('Starting offline booking creation', ['data' => $request->validated()]);

        // Find or create customer
        $customer = $this->findOrCreateCustomer($request);

        // Get room details
        $room = Room::with(['currency'])->findOrFail($request->input('room_id'));

        // Calculate booking details
        $bookingDetails = $this->calculateBookingDetails($request, $room);

        // Create the booking
        $booking = $this->createBooking($request, $customer, $room, $bookingDetails);

        // Create booking address
        $this->createBookingAddress($request, $booking);

        DB::commit();

        return redirect()
            ->route('booking.edit', $booking->id)
            ->with('success', 'Offline booking created successfully!');

    } catch (\Exception $e) {
        DB::rollBack();
        \Log::error('Offline booking creation failed: ' . $e->getMessage());
        return back()->withInput()->withErrors(['error' => $e->getMessage()]);
    }
}
```

**Customer Management**:
```php
// Find or create customer logic
private function findOrCreateCustomer($request)
{
    $customer = Customer::where('email', $request->input('email'))->first();

    if (!$customer) {
        $customer = Customer::create([
            'first_name' => $request->input('first_name'),
            'last_name' => $request->input('last_name'),
            'email' => $request->input('email'),
            'phone' => $request->input('phone'),
            'password' => Hash::make(Str::random(12)), // Random password for admin-created accounts
        ]);
    }

    return $customer;
}
```

**Booking Record Creation**:
```php
// Create booking with offline metadata
$booking = Booking::create([
    'customer_id' => $customer->id,
    'currency_id' => $room->currency_id,
    'amount' => $bookingDetails['total_amount'],
    'sub_total' => $bookingDetails['sub_total'],
    'tax_amount' => $bookingDetails['tax_amount'],
    'status' => $request->input('status', BookingStatusEnum::PENDING),
    'number_of_guests' => $request->input('adults'),
    'number_of_children' => $request->input('children', 0),
    'requests' => $request->input('requests'),
    'arrival_time' => $request->input('arrival_time'),
    'booking_number' => $bookingNumber,
    'transaction_id' => 'OFFLINE_' . Str::upper(Str::random(10)),
    'meta_data' => [
        'booking_type' => 'offline',
        'created_by_admin' => true,
        'admin_user_id' => auth()->id(),
        'payment_method' => $request->input('payment_method'),
        'payment_notes' => $request->input('payment_notes'),
    ]
]);
```

**Key Features**:
- **Transaction Safety**: Database transactions ensure data consistency
- **Comprehensive Logging**: Detailed logs for debugging and audit trails
- **Customer Deduplication**: Prevents duplicate customer records
- **Metadata Tracking**: Records offline booking source and admin details
- **Error Handling**: Graceful error handling with user feedback

---

## Feature Comparison

### Online vs Offline Booking Features

| Feature | Online Booking | Offline Booking | Notes |
|---------|---------------|-----------------|-------|
| **Room Display** | Shows all rooms with availability status | ✅ **Fixed**: Now shows all rooms with status badges | Previously showed fewer rooms |
| **Availability Check** | Real-time validation | Same validation logic | Consistent behavior |
| **Customer Creation** | Self-registration | Admin-managed | Different user flows |
| **Payment Processing** | Integrated payment gateway | Manual payment tracking | Online automated, offline manual |
| **Room Assignment** | Automatic assignment | Manual selection | Admin has more control |
| **Booking Status** | Starts as pending | Admin sets initial status | More flexibility offline |
| **Service Selection** | Customer selects | Admin selects for customer | Same options available |
| **Validation** | Client + server side | Server side | Online has additional UX validation |

### Detailed Feature Analysis

#### 1. Room Availability Display 🔧 **RECENTLY FIXED**

**Before Fix**:
- **Online**: Showed all rooms with availability indicators
- **Offline**: Filtered out unavailable rooms, showing fewer total rooms

**After Fix**:
- **Both systems**: Now show identical room sets with clear availability status
- **Visual Consistency**: Both use similar availability indicators
- **User Experience**: Admins can see full inventory like customers do

**Technical Implementation**:
```php
// Online Booking (PublicController::getRooms)
foreach ($allRooms as $allRoom) {
    $allRoom->is_available = $isAvailable && $isValidOccupancy;
    // Always add to results regardless of availability
}

// Offline Booking (OfflineBookingController::getAvailableRooms) - FIXED
$rooms = Room::with(['category', 'currency'])
    ->where('status', 'published')
    ->get()
    ->map(function ($room) use ($startDate, $endDate, $request) {
        // Calculate availability but return ALL rooms
        $isAvailable = $room->isAvailableAt($condition);
        $isValidOccupancy = $roomCapacityService->roomMatchesOccupancyRules($room, $adults, $children);

        return [
            'id' => $room->id,
            'name' => $room->name,
            'is_available' => $isAvailable && $isValidOccupancy,
            // ... other room data
        ];
    });
```

#### 2. User Experience Differences

**Online Booking Journey**:
1. **Self-Service**: Customer controls entire process
2. **Immediate Feedback**: Real-time validation and pricing
3. **Payment Integration**: Seamless payment processing
4. **Mobile Optimized**: Responsive design for all devices
5. **24/7 Availability**: No time restrictions

**Offline Booking Journey**:
1. **Assisted Service**: Admin guides the process
2. **Manual Verification**: Admin can verify details and make adjustments
3. **Flexible Payment**: Multiple payment options and manual tracking
4. **Desktop Optimized**: Admin interface designed for desktop use
5. **Business Hours**: Typically during office hours

#### 3. Data Flow Comparison

**Online Booking Data Flow**:
```
Customer Input → Form Validation → Availability Check → Session Storage →
Payment Processing → Booking Creation → Email Confirmation → Customer Portal
```

**Offline Booking Data Flow**:
```
Admin Input → Server Validation → Availability Check → Direct Database →
Manual Payment Tracking → Booking Creation → Admin Dashboard → Optional Customer Notification
```

#### 4. Security & Validation

**Online Booking Security**:
- CSRF protection on all forms
- Session-based booking tokens
- Payment gateway security
- Rate limiting on booking attempts
- Client-side + server-side validation

**Offline Booking Security**:
- Admin authentication required
- Role-based permissions (booking.create)
- Server-side validation only
- Database transaction safety
- Audit logging for admin actions

#### 5. Error Handling

**Online Booking Errors**:
- User-friendly error messages
- Graceful fallbacks for payment failures
- Session recovery mechanisms
- Automatic retry options

**Offline Booking Errors**:
- Detailed error logging for debugging
- Admin-focused error messages
- Transaction rollback on failures
- Manual intervention capabilities

### User Experience Differences

**Online Booking Advantages**:
- Self-service customer experience
- Immediate confirmation
- Automated payment processing
- 24/7 availability
- Mobile-responsive interface

**Offline Booking Advantages**:
- Admin oversight and control
- Manual verification possible
- Flexible payment options
- Can handle complex requests
- Override availability if needed

#### 6. Performance Considerations

**Online Booking Performance**:
- **Caching**: Room data cached for faster loading
- **CDN Integration**: Static assets served via CDN
- **Database Optimization**: Indexed queries for availability checks
- **Session Management**: Efficient session storage and cleanup
- **Load Balancing**: Supports horizontal scaling

**Offline Booking Performance**:
- **Admin Interface**: Optimized for desktop browsers
- **AJAX Loading**: Asynchronous room search for better UX
- **Database Transactions**: Ensures data consistency
- **Logging**: Comprehensive but optimized logging
- **Memory Usage**: Efficient memory management for large datasets

#### 7. Integration Capabilities

**Online Booking Integrations**:
- Payment gateways (Stripe, PayPal, etc.)
- Email service providers
- SMS notification services
- Third-party booking platforms
- Analytics and tracking tools

**Offline Booking Integrations**:
- Internal hotel management systems
- Accounting software integration
- Customer relationship management (CRM)
- Reporting and analytics tools
- Audit and compliance systems

#### 8. Customization & Extensibility

**Online Booking Customization**:
- Theme-based customization
- Custom booking flows
- Branded payment pages
- Multilingual support
- Custom validation rules

**Offline Booking Customization**:
- Admin interface customization
- Custom booking statuses
- Extended customer fields
- Custom reporting formats
- Role-based access controls

---

## Known Issues & Limitations

### 🔧 Recently Fixed Issues

#### 1. Room Availability Display Parity ✅ **RESOLVED**
**Previous Issue**: Offline booking showed fewer rooms than online booking for the same search criteria.

**Root Cause**: Offline booking filtered out unavailable rooms, while online booking showed all rooms with status indicators.

**Solution Applied**:
- Modified `OfflineBookingController::getAvailableRooms()` to return all rooms
- Added availability status to response data
- Enhanced frontend to display availability badges
- Added visual distinction between available/unavailable rooms

**Files Modified**:
- `platform/plugins/hotel/src/Http/Controllers/OfflineBookingController.php`
- `platform/plugins/hotel/resources/views/offline-booking/create.blade.php`

#### 2. Room Availability Logic Consistency ✅ **RESOLVED**
**Previous Issue**: Inconsistent availability checking between inventory-based and legacy room availability.

**Solution Applied**:
- Enhanced error handling in availability methods
- Improved consistency between different availability check modes
- Added comprehensive logging for debugging
- Better validation of date formats and ranges

**Files Modified**:
- `platform/plugins/hotel/src/Models/Room.php`
- `platform/plugins/hotel/src/Models/RoomInventory.php`

### Current Limitations

1. **Payment Integration**: Offline bookings require manual payment tracking
2. **Real-time Updates**: Room availability updates may have slight delays
3. **Mobile Optimization**: Admin interface is desktop-optimized
4. **Bulk Operations**: Limited bulk booking management features

---

## Technical Requirements

### System Requirements

#### Minimum Requirements
- **PHP**: 8.1 or higher with extensions:
  - BCMath, Ctype, Fileinfo, JSON, Mbstring, OpenSSL, PDO, Tokenizer, XML
- **Laravel**: 10.x framework
- **Database**: MySQL 8.0+ or PostgreSQL 13+
- **Web Server**: Apache 2.4+ or Nginx 1.18+
- **Memory**: Minimum 512MB RAM (2GB recommended)
- **Storage**: Minimum 1GB available space (5GB recommended)
- **SSL Certificate**: Required for production (payment processing)

#### Recommended Production Setup
- **PHP**: 8.2+ with OPcache enabled
- **Database**: MySQL 8.0+ with InnoDB engine
- **Web Server**: Nginx with PHP-FPM
- **Memory**: 4GB+ RAM
- **Storage**: SSD storage with 10GB+ available space
- **CDN**: CloudFlare or similar for static assets
- **Backup**: Automated daily backups

### Environment Configuration

#### Required Environment Variables
```bash
# Application
APP_NAME="MRhote Hotel Booking"
APP_ENV=production
APP_KEY=base64:your-app-key-here
APP_DEBUG=false
APP_URL=https://your-domain.com

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=mrhote_db
DB_USERNAME=your_username
DB_PASSWORD=your_password

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls

# Payment Gateways
STRIPE_KEY=pk_your_stripe_key
STRIPE_SECRET=sk_your_stripe_secret
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_secret

# File Storage
FILESYSTEM_DISK=public
AWS_ACCESS_KEY_ID=your_aws_key (if using S3)
AWS_SECRET_ACCESS_KEY=your_aws_secret
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=your_bucket_name
```

### Browser Compatibility

#### Online Booking (Customer-Facing)
- **Desktop**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **Mobile**: iOS Safari 14+, Chrome Mobile 90+, Samsung Internet 14+
- **Tablet**: iPad Safari 14+, Android Chrome 90+
- **Features**: Responsive design, touch-friendly interface

#### Offline Booking (Admin Interface)
- **Desktop**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+ (recommended)
- **Screen Resolution**: Minimum 1366x768 (1920x1080 recommended)
- **JavaScript**: Required for AJAX functionality
- **Cookies**: Required for session management

### Dependencies & Integrations

#### Core Dependencies
- **Botble CMS**: Latest stable version (6.x+)
- **Laravel Framework**: 10.x with security updates
- **PHP Extensions**: All Laravel required extensions
- **Composer**: 2.x for dependency management

#### Payment Gateway Integration
- **Stripe**: v3 API with SCA compliance
- **PayPal**: REST API v2
- **Razorpay**: v2 API (optional)
- **Bank Transfer**: Manual processing support

#### Third-Party Services
- **Email Service**:
  - SMTP (recommended)
  - SendGrid, Mailgun, Amazon SES (optional)
- **Image Processing**:
  - GD extension (minimum)
  - ImageMagick (recommended for better performance)
- **File Storage**:
  - Local storage (default)
  - Amazon S3 (recommended for production)
  - Google Cloud Storage (optional)

#### Development Tools
- **Node.js**: 16+ for asset compilation
- **NPM/Yarn**: For frontend dependencies
- **Webpack**: For asset bundling (included in Laravel Mix)
- **Git**: Version control system

### Installation & Setup Guide

#### 1. Server Preparation
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install PHP 8.2 and required extensions
sudo apt install php8.2 php8.2-fpm php8.2-mysql php8.2-xml php8.2-mbstring \
php8.2-curl php8.2-zip php8.2-gd php8.2-bcmath php8.2-intl

# Install Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

#### 2. Database Setup
```sql
-- Create database and user
CREATE DATABASE mrhote_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'mrhote_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON mrhote_db.* TO 'mrhote_user'@'localhost';
FLUSH PRIVILEGES;
```

#### 3. Application Installation
```bash
# Clone repository
git clone https://github.com/your-repo/mrhote.git
cd mrhote

# Install PHP dependencies
composer install --optimize-autoloader --no-dev

# Install Node.js dependencies
npm install

# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate

# Configure environment variables (edit .env file)
nano .env

# Run database migrations
php artisan migrate

# Seed initial data
php artisan db:seed

# Build frontend assets
npm run production

# Set proper permissions
sudo chown -R www-data:www-data storage bootstrap/cache
sudo chmod -R 775 storage bootstrap/cache
```

#### 4. Web Server Configuration

**Nginx Configuration** (`/etc/nginx/sites-available/mrhote`):
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/mrhote/public;
    index index.php index.html;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.ht {
        deny all;
    }
}
```

#### 5. SSL Certificate Setup
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal setup
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

#### 6. Performance Optimization
```bash
# Enable OPcache
echo "opcache.enable=1" >> /etc/php/8.2/fpm/php.ini
echo "opcache.memory_consumption=256" >> /etc/php/8.2/fpm/php.ini
echo "opcache.max_accelerated_files=20000" >> /etc/php/8.2/fpm/php.ini

# Configure Laravel caching
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Setup queue worker (for background jobs)
sudo nano /etc/systemd/system/mrhote-worker.service
```

#### 7. Backup Configuration
```bash
# Database backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u mrhote_user -p mrhote_db > /backups/mrhote_db_$DATE.sql
find /backups -name "mrhote_db_*.sql" -mtime +7 -delete

# Add to crontab for daily backups
0 2 * * * /path/to/backup-script.sh
```

---

## Demo Walkthrough

### Preparation Checklist
- [ ] Ensure demo environment is running
- [ ] Verify sample room data is loaded
- [ ] Test both booking flows
- [ ] Prepare sample customer data
- [ ] Check payment gateway (sandbox mode)

### Demo Script

#### Part 1: Online Booking Demo (10 minutes)
1. **Homepage Tour** (2 min)
   - Show main navigation
   - Highlight booking call-to-action
   - Demonstrate responsive design

2. **Room Search** (3 min)
   - Use search form with specific dates
   - Show room filtering in action
   - Explain availability indicators

3. **Booking Process** (5 min)
   - Select a room and initiate booking
   - Fill customer information form
   - Demonstrate payment process (sandbox)
   - Show confirmation page

#### Part 2: Offline Booking Demo (10 minutes)
1. **Admin Access** (2 min)
   - Login to admin dashboard
   - Navigate to booking management
   - Show offline booking option

2. **Booking Creation** (6 min)
   - Enter customer information
   - Set booking parameters
   - Demonstrate room search with all rooms visible
   - Show availability status indicators
   - Complete booking creation

3. **Comparison Highlight** (2 min)
   - Show both booking lists
   - Explain the parity fix
   - Demonstrate admin oversight capabilities

#### Part 3: Management Features (5 minutes)
1. **Booking Management**
   - Show booking calendar
   - Demonstrate status updates
   - Explain reporting features

2. **Room Management**
   - Show room inventory
   - Explain availability management
   - Demonstrate pricing controls

### Key Talking Points
- **Flexibility**: System handles both self-service and assisted bookings
- **Reliability**: Recent fixes ensure consistent behavior
- **Scalability**: Built on robust Laravel/Botble foundation
- **User Experience**: Intuitive interfaces for both customers and staff
- **Integration**: Ready for payment gateways and third-party services

### Demo Environment URLs
- **Frontend (Online Booking)**: `https://your-domain.com`
- **Admin Dashboard**: `https://your-domain.com/admin`
- **Offline Booking**: `https://your-domain.com/admin/hotel/booking/offline/create`
- **Booking Management**: `https://your-domain.com/admin/hotel/booking`
- **Room Management**: `https://your-domain.com/admin/hotel/room`

### Sample Demo Data
- **Test Customer**: <EMAIL> / password123
- **Admin User**: <EMAIL> / admin123
- **Sample Rooms**: Standard, Deluxe, Suite, Luxury categories
- **Test Dates**: Use current date + 1 day for check-in
- **Payment**: Use Stripe test cards (4242 4242 4242 4242)

---

## Appendix

### A. Troubleshooting Common Issues

#### Issue 1: Room Availability Discrepancy
**Symptoms**: Different room counts between online and offline booking
**Solution**: Ensure latest fixes are applied (see OFFLINE_BOOKING_ONLINE_PARITY_FIXES.md)
**Verification**: Check both booking flows with identical search parameters

#### Issue 2: Payment Gateway Errors
**Symptoms**: Payment processing failures
**Solution**: Verify API keys and webhook configurations
**Testing**: Use sandbox/test mode for demonstrations

#### Issue 3: Email Notifications Not Sending
**Symptoms**: Customers not receiving booking confirmations
**Solution**: Check SMTP configuration and email queue processing
**Testing**: Send test emails from admin panel

### B. API Endpoints Reference

#### Public API Endpoints
- `GET /api/rooms` - List available rooms
- `POST /api/booking` - Create booking
- `GET /api/booking/{id}` - Get booking details

#### Admin API Endpoints
- `GET /admin/api/booking/offline/rooms` - Get rooms for offline booking
- `POST /admin/api/booking/offline` - Create offline booking
- `PUT /admin/api/booking/{id}` - Update booking

### C. Database Schema Overview

#### Key Tables
- `ht_rooms` - Room information and pricing
- `ht_bookings` - Booking records
- `ht_customers` - Customer information
- `ht_booking_rooms` - Booking-room relationships
- `ht_room_inventory` - Room availability tracking

### D. Security Considerations

#### Data Protection
- Customer data encryption at rest
- PCI DSS compliance for payment data
- GDPR compliance for EU customers
- Regular security audits and updates

#### Access Control
- Role-based admin permissions
- Session timeout configuration
- Failed login attempt limiting
- Two-factor authentication (optional)

### E. Performance Monitoring

#### Key Metrics to Monitor
- Page load times (< 3 seconds target)
- Database query performance
- Payment processing success rate
- Email delivery rates
- Server resource utilization

#### Recommended Tools
- Laravel Telescope for debugging
- New Relic or similar for APM
- Google Analytics for user behavior
- Uptime monitoring services

---

## Contact Information

**Technical Support**: <EMAIL>
**Documentation**: docs.mrhote.com
**Repository**: github.com/your-org/mrhote
**Demo Environment**: demo.mrhote.com

---

*This comprehensive demo guide provides everything needed to showcase the MRhote booking system effectively. For additional technical details, customization options, or integration support, please contact our technical team.*

**Document Version**: 1.0
**Last Updated**: {{ date('Y-m-d') }}
**Prepared for**: Client Demo Presentation
